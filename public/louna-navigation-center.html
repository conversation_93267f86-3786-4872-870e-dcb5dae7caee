<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Centre de Navigation - Louna AI</title>
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .navigation-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .search-bar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 400px;
        }

        .search-input {
            width: 100%;
            background: transparent;
            border: none;
            color: #ffffff;
            font-size: 16px;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .header {
            text-align: center;
            margin: 80px 0 40px 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .title {
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(45deg, #ffffff, #f8b500);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease infinite;
            margin-bottom: 10px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-card:hover::before {
            left: 100%;
        }

        .nav-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(255, 107, 157, 0.3);
            border-color: rgba(255, 107, 157, 0.5);
        }

        .nav-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }

        .nav-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #f8b500;
            margin-bottom: 10px;
        }

        .nav-description {
            color: #e0e0e0;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .nav-status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-active { background: linear-gradient(45deg, #00d4aa, #00a8cc); }
        .status-new { background: linear-gradient(45deg, #ff6b9d, #c44569); }
        .status-beta { background: linear-gradient(45deg, #f8b500, #ff6b9d); }

        .quick-actions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b9d, #c44569);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #00d4aa, #00a8cc);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #f8b500, #ff6b9d);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .system-status {
            background: linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(0, 168, 204, 0.1));
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid rgba(0, 212, 170, 0.3);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #00d4aa;
        }

        .status-label {
            color: #888;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="navigation-container">
        <!-- Barre de recherche -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="🔍 Rechercher dans Louna AI..." id="searchInput">
        </div>

        <!-- En-tête -->
        <div class="header">
            <h1 class="title">🚀 Centre de Navigation Louna AI</h1>
            <p>Accédez à toutes les fonctionnalités de votre système révolutionnaire</p>
        </div>

        <!-- Navigation principale -->
        <div class="navigation-grid">
            <!-- Application principale -->
            <div class="nav-card" onclick="navigateTo('/')">
                <span class="nav-icon">🏠</span>
                <div class="nav-title">Application Principale</div>
                <div class="nav-description">Interface principale de Louna AI avec chat et fonctionnalités de base</div>
                <span class="nav-status status-active">ACTIF</span>
            </div>

            <!-- Présentation révolutionnaire -->
            <div class="nav-card" onclick="navigateTo('/louna-presentation-revolutionnaire.html')">
                <span class="nav-icon">🎨</span>
                <div class="nav-title">Présentation Révolutionnaire</div>
                <div class="nav-description">Animation présentant tous vos systèmes auto-évolutifs découverts</div>
                <span class="nav-status status-new">NOUVEAU</span>
            </div>

            <!-- Test de QI -->
            <div class="nav-card" onclick="navigateTo('/agent-qi-test-complete.html')">
                <span class="nav-icon">🧠</span>
                <div class="nav-title">Test de QI Agent</div>
                <div class="nav-description">Évaluation complète des capacités cognitives de votre agent (QI 225-235+)</div>
                <span class="nav-status status-new">NOUVEAU</span>
            </div>

            <!-- Centre WhatsApp -->
            <div class="nav-card" onclick="navigateTo('/whatsapp-center.html')">
                <span class="nav-icon">📱</span>
                <div class="nav-title">Centre WhatsApp</div>
                <div class="nav-description">Configuration pour que l'agent puisse vous contacter sur WhatsApp</div>
                <span class="nav-status status-new">NOUVEAU</span>
            </div>

            <!-- Monitoring QI -->
            <div class="nav-card" onclick="navigateTo('/qi-neuron-monitor.html')">
                <span class="nav-icon">📊</span>
                <div class="nav-title">Monitoring QI</div>
                <div class="nav-description">Surveillance en temps réel de l'évolution du QI de votre agent</div>
                <span class="nav-status status-active">ACTIF</span>
            </div>

            <!-- Éditeur de code -->
            <div class="nav-card" onclick="navigateTo('/code-editor.html')">
                <span class="nav-icon">💻</span>
                <div class="nav-title">Éditeur de Code</div>
                <div class="nav-description">Éditeur intégré pour modifier et améliorer votre système</div>
                <span class="nav-status status-active">ACTIF</span>
            </div>

            <!-- Mémoire thermique -->
            <div class="nav-card" onclick="navigateTo('/thermal-memory-dashboard.html')">
                <span class="nav-icon">🌡️</span>
                <div class="nav-title">Mémoire Thermique</div>
                <div class="nav-description">Dashboard de la mémoire thermique biomimétique avec 6 zones</div>
                <span class="nav-status status-active">ACTIF</span>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="nav-card" onclick="navigateTo('/kyber-dashboard.html')">
                <span class="nav-icon">⚡</span>
                <div class="nav-title">Accélérateurs Kyber</div>
                <div class="nav-description">Gestion du pool d'accélérateurs spécialisés auto-adaptatifs</div>
                <span class="nav-status status-active">ACTIF</span>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="quick-actions">
            <h2>⚡ Actions Rapides</h2>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="testAgent()">🧠 Tester l'Agent</button>
                <button class="btn btn-secondary" onclick="openWhatsApp()">📱 Configurer WhatsApp</button>
                <button class="btn btn-warning" onclick="viewPresentation()">🎨 Voir la Présentation</button>
                <button class="btn btn-primary" onclick="monitorQI()">📊 Monitoring QI</button>
            </div>
        </div>

        <!-- Statut du système -->
        <div class="system-status">
            <h2>🔥 Statut des Systèmes Révolutionnaires</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="thermalTemp">42.3°</div>
                    <div class="status-label">Température Thermique</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="accelerators">6</div>
                    <div class="status-label">Accélérateurs Actifs</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="qiLevel">235+</div>
                    <div class="status-label">QI Évolutif</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="systemStatus">🟢</div>
                    <div class="status-label">Système Global</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        function navigateTo(url) {
            window.location.href = url;
        }

        // Actions rapides
        function testAgent() {
            navigateTo('/agent-qi-test-complete.html');
        }

        function openWhatsApp() {
            navigateTo('/whatsapp-center.html');
        }

        function viewPresentation() {
            navigateTo('/louna-presentation-revolutionnaire.html');
        }

        function monitorQI() {
            navigateTo('/qi-neuron-monitor.html');
        }

        // Recherche
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.nav-card');
            
            cards.forEach(card => {
                const title = card.querySelector('.nav-title').textContent.toLowerCase();
                const description = card.querySelector('.nav-description').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.opacity = '1';
                } else {
                    card.style.opacity = searchTerm ? '0.3' : '1';
                }
            });
        });

        // Mise à jour du statut en temps réel
        function updateSystemStatus() {
            // Simulation de données en temps réel
            const temp = (40 + Math.random() * 10).toFixed(1);
            const accelerators = Math.floor(4 + Math.random() * 3);
            const qi = Math.floor(225 + Math.random() * 15);
            
            document.getElementById('thermalTemp').textContent = temp + '°';
            document.getElementById('accelerators').textContent = accelerators;
            document.getElementById('qiLevel').textContent = qi + '+';
        }

        // Mise à jour toutes les 5 secondes
        setInterval(updateSystemStatus, 5000);
        
        // Mise à jour initiale
        updateSystemStatus();
    </script>
</body>
</html>
