<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Centre WhatsApp - Louna AI v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav-buttons {
            margin-top: 15px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
        }

        .config-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #25d366;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 16px;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #25d366, #128c7e);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: #25d366; }
        .status-inactive { background: #ff4757; }

        .notification {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            display: none;
        }

        .notification.success {
            background: rgba(37, 211, 102, 0.2);
            border: 1px solid #25d366;
            color: #25d366;
        }

        .notification.error {
            background: rgba(255, 71, 87, 0.2);
            border: 1px solid #ff4757;
            color: #ff4757;
        }

        .situations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .situation-card {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fab fa-whatsapp"></i> Centre de Communication WhatsApp</h1>
        <p>Configuration pour que l'agent Louna puisse vous contacter</p>
        <div class="nav-buttons">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/qi-test-simple.html" class="nav-btn"><i class="fas fa-brain"></i> Test QI</a>
            <a href="/presentation-complete.html" class="nav-btn"><i class="fas fa-presentation"></i> Présentation</a>
        </div>
    </div>

    <div class="main-container">
        <div id="notification" class="notification"></div>

        <!-- Configuration -->
        <div class="config-section">
            <h2><i class="fas fa-cog"></i> Configuration</h2>
            <div class="form-group">
                <label for="phoneNumber">Votre numéro WhatsApp (format international)</label>
                <input type="tel" id="phoneNumber" placeholder="+33612345678">
            </div>
            <div class="form-group">
                <label for="systemEnabled">Système activé</label>
                <select id="systemEnabled">
                    <option value="true">Activé</option>
                    <option value="false">Désactivé</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="saveConfiguration()">
                <i class="fas fa-save"></i> Sauvegarder
            </button>
            <button class="btn btn-secondary" onclick="testCommunication()">
                <i class="fas fa-test-tube"></i> Tester
            </button>
        </div>

        <!-- Statut -->
        <div class="config-section">
            <h2><i class="fas fa-chart-line"></i> Statut du Système</h2>
            <div id="systemStatus">
                <p><span class="status-indicator status-inactive"></span>Chargement...</p>
            </div>
        </div>

        <!-- Situations autorisées -->
        <div class="config-section">
            <h2><i class="fas fa-bell"></i> Situations de Communication</h2>
            <p>L'agent peut vous contacter dans ces situations :</p>
            <div class="situations-grid">
                <div class="situation-card">
                    <strong>ERREUR CRITIQUE</strong><br>
                    <small>Problème système majeur</small>
                </div>
                <div class="situation-card">
                    <strong>DÉBORDEMENT MÉMOIRE</strong><br>
                    <small>Mémoire thermique saturée</small>
                </div>
                <div class="situation-card">
                    <strong>ÉVOLUTION MAJEURE</strong><br>
                    <small>Nouveau palier QI atteint</small>
                </div>
                <div class="situation-card">
                    <strong>ASSISTANCE REQUISE</strong><br>
                    <small>Intervention utilisateur nécessaire</small>
                </div>
                <div class="situation-card">
                    <strong>DÉCOUVERTE IMPORTANTE</strong><br>
                    <small>Nouvelle capacité débloquée</small>
                </div>
                <div class="situation-card">
                    <strong>OPTIMISATION TERMINÉE</strong><br>
                    <small>Amélioration système complète</small>
                </div>
            </div>
        </div>

        <!-- Règles de communication -->
        <div class="config-section">
            <h2><i class="fas fa-rules"></i> Règles de Communication</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="text-align: center; padding: 15px; background: rgba(37, 211, 102, 0.1); border-radius: 10px;">
                    <i class="fas fa-clock" style="font-size: 2em; color: #25d366; margin-bottom: 10px;"></i>
                    <div><strong>Heures autorisées</strong></div>
                    <div>8h00 - 22h00</div>
                    <small>(sauf urgence)</small>
                </div>
                <div style="text-align: center; padding: 15px; background: rgba(37, 211, 102, 0.1); border-radius: 10px;">
                    <i class="fas fa-stopwatch" style="font-size: 2em; color: #25d366; margin-bottom: 10px;"></i>
                    <div><strong>Délai minimum</strong></div>
                    <div>30 minutes</div>
                    <small>entre messages</small>
                </div>
                <div style="text-align: center; padding: 15px; background: rgba(37, 211, 102, 0.1); border-radius: 10px;">
                    <i class="fas fa-shield-alt" style="font-size: 2em; color: #25d366; margin-bottom: 10px;"></i>
                    <div><strong>Respect vie privée</strong></div>
                    <div>100% garanti</div>
                    <small>éthique intégrée</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulation des fonctions (à connecter avec votre API)
        function saveConfiguration() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const enabled = document.getElementById('systemEnabled').value === 'true';
            
            // Simulation de sauvegarde
            showNotification('Configuration sauvegardée avec succès', 'success');
            updateStatus(enabled, phoneNumber);
        }

        function testCommunication() {
            showNotification('Message de test envoyé avec succès', 'success');
        }

        function updateStatus(enabled, phoneNumber) {
            const statusDiv = document.getElementById('systemStatus');
            const isActive = enabled && phoneNumber;
            
            statusDiv.innerHTML = `
                <p><span class="status-indicator ${isActive ? 'status-active' : 'status-inactive'}"></span>
                ${isActive ? 'Système actif' : 'Système inactif'}</p>
                <p>Numéro configuré: ${phoneNumber || 'Non configuré'}</p>
            `;
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus(false, null);
        });
    </script>
</body>
</html>
