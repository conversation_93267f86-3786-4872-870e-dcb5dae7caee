/**
 * 📱 ROUTES API POUR LA COMMUNICATION WHATSAPP
 * Gestion des communications entre l'agent et l'utilisateur
 */

const express = require('express');
const router = express.Router();
const AgentWhatsAppCommunication = require('../agent-whatsapp-communication');

// Instance globale du système de communication
let whatsappComm = null;

/**
 * Initialise le système de communication WhatsApp
 */
function initializeWhatsAppCommunication() {
    if (!whatsappComm) {
        whatsappComm = new AgentWhatsAppCommunication();
    }
    return whatsappComm;
}

/**
 * GET /api/whatsapp/status
 * Obtenir le statut du système de communication
 */
router.get('/status', (req, res) => {
    try {
        const comm = initializeWhatsAppCommunication();
        const stats = comm.getStats();
        
        res.json({
            success: true,
            status: stats,
            message: 'Statut du système de communication WhatsApp'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/whatsapp/configure
 * Configurer le numéro de téléphone et les paramètres
 */
router.post('/configure', (req, res) => {
    try {
        const { phoneNumber, enabled } = req.body;
        const comm = initializeWhatsAppCommunication();
        
        if (phoneNumber) {
            comm.setUserPhoneNumber(phoneNumber);
        }
        
        if (typeof enabled === 'boolean') {
            comm.setEnabled(enabled);
        }
        
        res.json({
            success: true,
            message: 'Configuration WhatsApp mise à jour',
            status: comm.getStats()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/whatsapp/contact
 * L'agent utilise cette route pour vous contacter
 */
router.post('/contact', async (req, res) => {
    try {
        const { situation, message, priority = 'info' } = req.body;
        
        if (!situation || !message) {
            return res.status(400).json({
                success: false,
                error: 'Situation et message requis'
            });
        }
        
        const comm = initializeWhatsAppCommunication();
        const success = await comm.contactUser(situation, message, priority);
        
        res.json({
            success,
            message: success ? 'Message WhatsApp envoyé' : 'Communication bloquée ou échouée',
            situation,
            priority
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/whatsapp/history
 * Obtenir l'historique des communications
 */
router.get('/history', (req, res) => {
    try {
        const comm = initializeWhatsAppCommunication();
        const limit = parseInt(req.query.limit) || 50;
        
        const history = comm.communicationHistory
            .slice(-limit)
            .reverse(); // Plus récents en premier
        
        res.json({
            success: true,
            history,
            total: comm.communicationHistory.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/whatsapp/test
 * Tester le système de communication
 */
router.post('/test', async (req, res) => {
    try {
        const comm = initializeWhatsAppCommunication();
        const success = await comm.contactUser(
            'system_test',
            'Test de communication WhatsApp depuis l\'interface d\'administration',
            'info'
        );
        
        res.json({
            success,
            message: success ? 'Message de test envoyé' : 'Test échoué'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/whatsapp/situations
 * Obtenir la liste des situations autorisées
 */
router.get('/situations', (req, res) => {
    try {
        const comm = initializeWhatsAppCommunication();
        
        res.json({
            success: true,
            situations: comm.communicationRules.allowedSituations,
            messageTypes: comm.communicationRules.messageTypes,
            rules: {
                allowedHours: comm.communicationRules.allowedHours,
                minimumDelay: comm.communicationRules.minimumDelay
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le router et la fonction d'initialisation
module.exports = {
    router,
    initializeWhatsAppCommunication
};
