// Evolution Monitor - Module simplifié pour Louna AI
class EvolutionMonitor {
    constructor() {
        this.monitoring = true;
        this.status = 'active';
    }

    getStatus() {
        return {
            monitoring: this.monitoring,
            status: this.status,
            timestamp: new Date().toISOString()
        };
    }

    startMonitoring() {
        this.monitoring = true;
    }

    stopMonitoring() {
        this.monitoring = false;
    }
}

module.exports = EvolutionMonitor;
