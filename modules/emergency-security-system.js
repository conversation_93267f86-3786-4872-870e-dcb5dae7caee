// Emergency Security System - Module simplifié pour Louna AI
class EmergencySecuritySystem {
    constructor() {
        this.active = true;
        this.securityLevel = 'high';
    }

    isActive() {
        return this.active;
    }

    getSecurityStatus() {
        return {
            active: this.active,
            securityLevel: this.securityLevel,
            timestamp: new Date().toISOString()
        };
    }

    activateEmergencyMode() {
        this.securityLevel = 'emergency';
        console.log('🚨 Mode d\'urgence activé');
    }
}

module.exports = EmergencySecuritySystem;
