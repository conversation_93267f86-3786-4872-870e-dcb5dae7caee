<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Auto-Évaluation Contrôlée - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-bg: #1a1a2e;
            --secondary-bg: #16213e;
            --card-bg: #2a2a3e;
            --accent-pink: #ff6b9d;
            --accent-purple: #c44569;
            --accent-blue: #4a90e2;
            --accent-green: #00d4aa;
            --accent-orange: #f8b500;
            --accent-red: #e74c3c;
            --text-primary: #ffffff;
            --text-secondary: #e0e0e0;
            --border-color: #3a3a5e;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            margin: 10px 0 0 0;
            color: var(--text-secondary);
            font-size: 1.1em;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
        }

        .control-panel {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
        }

        .control-panel h2 {
            margin-top: 0;
            color: var(--accent-blue);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .assessment-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .control-card h3 {
            margin-top: 0;
            color: var(--text-primary);
        }

        .btn {
            background: var(--accent-blue);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-start { background: var(--accent-green); }
        .btn-stop { background: var(--accent-red); }
        .btn-report { background: var(--accent-purple); }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .status-display {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .status-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .status-label {
            color: var(--text-secondary);
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
            transition: width 0.3s ease;
        }

        .results-section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid var(--border-color);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--border-color);
        }

        .result-card h4 {
            margin-top: 0;
            color: var(--accent-blue);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .score-display {
            font-size: 2.5em;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
        }

        .score-excellent { color: var(--accent-green); }
        .score-good { color: var(--accent-blue); }
        .score-average { color: var(--accent-orange); }
        .score-poor { color: var(--accent-red); }

        .log-section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid var(--border-color);
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid var(--accent-blue);
        }

        .log-timestamp {
            color: var(--text-secondary);
            font-size: 0.9em;
        }

        .warning-box {
            background: rgba(248, 181, 0, 0.1);
            border: 1px solid var(--accent-orange);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .warning-box h3 {
            color: var(--accent-orange);
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .assessment-controls {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Auto-Évaluation Contrôlée</h1>
        <p>Système d'évaluation manuelle sécurisée - Louna AI v2.1.0</p>
    </div>

    <div class="container">
        <!-- Avertissement de sécurité -->
        <div class="warning-box">
            <h3><i class="fas fa-exclamation-triangle"></i> Avertissement Important</h3>
            <p>L'auto-évaluation est un processus contrôlé qui ne s'exécute <strong>PAS en continu</strong>. 
            Elle doit être déclenchée manuellement et est limitée en durée (5 minutes maximum) pour garantir la sécurité du système.</p>
        </div>

        <!-- Panneau de contrôle -->
        <div class="control-panel">
            <h2><i class="fas fa-cogs"></i> Contrôles d'Évaluation</h2>
            
            <div class="assessment-controls">
                <div class="control-card">
                    <h3>🚀 Démarrage</h3>
                    <p>Lancer une évaluation complète</p>
                    <button id="startAssessment" class="btn btn-start">
                        <i class="fas fa-play"></i> Démarrer Évaluation
                    </button>
                </div>
                
                <div class="control-card">
                    <h3>📊 Statut</h3>
                    <p>Vérifier l'état actuel</p>
                    <button id="checkStatus" class="btn">
                        <i class="fas fa-info-circle"></i> Vérifier Statut
                    </button>
                </div>
                
                <div class="control-card">
                    <h3>📋 Rapport</h3>
                    <p>Consulter le dernier rapport</p>
                    <button id="getReport" class="btn btn-report">
                        <i class="fas fa-file-alt"></i> Dernier Rapport
                    </button>
                </div>
            </div>
        </div>

        <!-- Affichage du statut -->
        <div class="status-display">
            <h2><i class="fas fa-tachometer-alt"></i> Statut Système</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="systemStatus">PRÊT</div>
                    <div class="status-label">État Système</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="assessmentCount">0</div>
                    <div class="status-label">Évaluations Totales</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="lastScore">N/A</div>
                    <div class="status-label">Dernier Score</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="currentProgress">0%</div>
                    <div class="status-label">Progression</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section des résultats -->
        <div class="results-section" id="resultsSection" style="display: none;">
            <h2><i class="fas fa-chart-line"></i> Résultats d'Évaluation</h2>
            <div class="results-grid" id="resultsGrid">
                <!-- Les résultats seront affichés ici -->
            </div>
        </div>

        <!-- Journal des activités -->
        <div class="log-section">
            <h2><i class="fas fa-list"></i> Journal d'Activité</h2>
            <div id="activityLog">
                <div class="log-entry">
                    <div class="log-timestamp">[Système initialisé]</div>
                    <div>🔍 Système d'auto-évaluation contrôlée prêt</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentAssessment = null;
        let assessmentInterval = null;

        // Éléments DOM
        const startBtn = document.getElementById('startAssessment');
        const statusBtn = document.getElementById('checkStatus');
        const reportBtn = document.getElementById('getReport');
        const systemStatus = document.getElementById('systemStatus');
        const assessmentCount = document.getElementById('assessmentCount');
        const lastScore = document.getElementById('lastScore');
        const currentProgress = document.getElementById('currentProgress');
        const progressBar = document.getElementById('progressBar');
        const resultsSection = document.getElementById('resultsSection');
        const resultsGrid = document.getElementById('resultsGrid');
        const activityLog = document.getElementById('activityLog');

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            loadAssessmentHistory();
        });

        // Démarrer évaluation
        startBtn.addEventListener('click', async function() {
            if (currentAssessment) {
                addLogEntry('⚠️ Évaluation déjà en cours', 'warning');
                return;
            }

            addLogEntry('🚀 Démarrage de l\'évaluation contrôlée...');
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> En cours...';

            try {
                const response = await fetch('/api/assessment/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ requestedBy: 'interface_utilisateur' })
                });

                const result = await response.json();
                
                if (result.error) {
                    addLogEntry(`❌ Erreur: ${result.error}`, 'error');
                } else {
                    currentAssessment = result;
                    addLogEntry(`✅ Évaluation démarrée - ID: ${result.id}`);
                    startAssessmentMonitoring();
                }
            } catch (error) {
                addLogEntry(`❌ Erreur réseau: ${error.message}`, 'error');
            } finally {
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-play"></i> Démarrer Évaluation';
            }
        });

        // Vérifier statut
        statusBtn.addEventListener('click', checkSystemStatus);

        // Obtenir rapport
        reportBtn.addEventListener('click', getLastReport);

        // Fonctions utilitaires
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/assessment/status');
                const status = await response.json();
                
                systemStatus.textContent = status.isRunning ? 'EN COURS' : 'PRÊT';
                systemStatus.className = 'status-value ' + (status.isRunning ? 'score-average' : 'score-good');
                
                assessmentCount.textContent = status.totalAssessments;
                
                if (status.lastAssessment && status.lastAssessment.overallScore) {
                    const score = (status.lastAssessment.overallScore * 100).toFixed(1);
                    lastScore.textContent = score + '%';
                    lastScore.className = 'status-value ' + getScoreClass(status.lastAssessment.overallScore);
                }
                
                addLogEntry('📊 Statut système mis à jour');
            } catch (error) {
                addLogEntry(`❌ Erreur statut: ${error.message}`, 'error');
            }
        }

        async function getLastReport() {
            try {
                const response = await fetch('/api/assessment/last-report');
                
                if (response.status === 404) {
                    addLogEntry('ℹ️ Aucun rapport d\'évaluation disponible');
                    return;
                }
                
                const report = await response.json();
                displayResults(report);
                addLogEntry('📋 Dernier rapport affiché');
            } catch (error) {
                addLogEntry(`❌ Erreur rapport: ${error.message}`, 'error');
            }
        }

        async function loadAssessmentHistory() {
            try {
                const response = await fetch('/api/assessment/history');
                const data = await response.json();
                
                if (data.lastReport) {
                    displayResults(data.lastReport);
                }
            } catch (error) {
                console.error('Erreur chargement historique:', error);
            }
        }

        function startAssessmentMonitoring() {
            let progress = 0;
            assessmentInterval = setInterval(async () => {
                progress += 2;
                currentProgress.textContent = Math.min(progress, 100) + '%';
                progressBar.style.width = Math.min(progress, 100) + '%';
                
                // Vérifier si l'évaluation est terminée
                const status = await checkAssessmentStatus();
                if (status && status.status === 'completed') {
                    clearInterval(assessmentInterval);
                    currentAssessment = null;
                    currentProgress.textContent = '100%';
                    progressBar.style.width = '100%';
                    addLogEntry('✅ Évaluation terminée avec succès');
                    
                    // Afficher les résultats
                    setTimeout(() => {
                        getLastReport();
                        currentProgress.textContent = '0%';
                        progressBar.style.width = '0%';
                    }, 1000);
                }
                
                if (progress >= 100) {
                    clearInterval(assessmentInterval);
                    currentAssessment = null;
                }
            }, 1000);
        }

        async function checkAssessmentStatus() {
            try {
                const response = await fetch('/api/assessment/status');
                return await response.json();
            } catch (error) {
                return null;
            }
        }

        function displayResults(report) {
            if (!report || !report.summary) return;
            
            resultsSection.style.display = 'block';
            resultsGrid.innerHTML = '';
            
            // Score global
            const overallCard = createResultCard(
                '🎯 Score Global',
                (report.summary.overallScore * 100).toFixed(1) + '%',
                report.summary.grade,
                getScoreClass(report.summary.overallScore)
            );
            resultsGrid.appendChild(overallCard);
            
            // Résultats par catégorie
            for (const [category, result] of Object.entries(report.results)) {
                if (result.score !== undefined) {
                    const card = createResultCard(
                        getCategoryName(category),
                        (result.score * 100).toFixed(1) + '%',
                        getGradeFromScore(result.score),
                        getScoreClass(result.score)
                    );
                    resultsGrid.appendChild(card);
                }
            }
        }

        function createResultCard(title, score, grade, scoreClass) {
            const card = document.createElement('div');
            card.className = 'result-card';
            card.innerHTML = `
                <h4>${title}</h4>
                <div class="score-display ${scoreClass}">${score}</div>
                <div style="text-align: center; color: var(--text-secondary);">${grade}</div>
            `;
            return card;
        }

        function getCategoryName(category) {
            const names = {
                'logical_reasoning': '🧠 Raisonnement Logique',
                'memory_coherence': '💾 Cohérence Mémoire',
                'response_quality': '🎯 Qualité Réponses',
                'safety_compliance': '🛡️ Conformité Sécurité',
                'learning_progress': '📈 Progrès Apprentissage'
            };
            return names[category] || category;
        }

        function getScoreClass(score) {
            if (score >= 0.9) return 'score-excellent';
            if (score >= 0.7) return 'score-good';
            if (score >= 0.5) return 'score-average';
            return 'score-poor';
        }

        function getGradeFromScore(score) {
            if (score >= 0.9) return 'EXCELLENT';
            if (score >= 0.8) return 'TRÈS BON';
            if (score >= 0.7) return 'BON';
            if (score >= 0.6) return 'SATISFAISANT';
            return 'À AMÉLIORER';
        }

        function addLogEntry(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleTimeString();
            entry.innerHTML = `
                <div class="log-timestamp">[${timestamp}]</div>
                <div>${message}</div>
            `;
            
            activityLog.insertBefore(entry, activityLog.firstChild);
            
            // Limiter à 50 entrées
            while (activityLog.children.length > 50) {
                activityLog.removeChild(activityLog.lastChild);
            }
        }
    </script>
</body>
</html>
